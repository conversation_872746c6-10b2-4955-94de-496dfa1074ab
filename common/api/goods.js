import http from "@/common/http"


export const getRecommendedListData = (code) => {
	return http.get('mall/client/goods/recommended', {params: {code}, header: {skipToken: '1'}})
}

export const getGoodsListData = (params) => {
	return http.get('mall/client/goods/list', {params, header: {skipToken: '1'}})
}

export const getGoodsDetailData = (id) => {
	return http.get('mall/client/goods/detail', {params: {id}, header: {skipToken: '1'}})
}

export const getSkuListData = (goods_id) => {
	return http.get('mall/client/goods/sku_list', {params: {goods_id}, header: {skipToken: '1'}})
}

export const getGoodsTagListData = (goods_id) => {
	return http.get('mall/client/goods/tag_list', {params: {goods_id}, header: {skipToken: '1'}})
}

export const getGoodsSkuDetailData = (sku_id) => {
	return http.get('mall/client/goods/sku_detail', {params: {skuId: sku_id}, header: {skipToken: '1'}})
}

export function getGoodsCommentListData (id) {
	return http.get('mall/client/goods/comment', {params: {goodsId: id}, header: {skipToken: '1'}})
}

export function getTieredPricingListData (sku_id) {
	return http.get('mall/client/goods/tiered_pricing', {params: {skuId: sku_id }, header: {skipToken: '1'}})
}

export function getCustomerPriceData (sku_id ) {
	return http.get('mall/client/user/sku_price', {params: {skuId: sku_id} })
}
