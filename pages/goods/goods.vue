<template>
  <view>
    <!-- 01. 轮播区 -->
    <view class="swiper-area w-full pos-f">
      <swiper class="h-full pos-r" indicator-dots="true" circular="true" duration="400">
        <swiper-item v-for="(item, index) in goods.imageList" :key="index">
          <view class="wh-full">
            <image :src="item" class="wh-full loaded" lazy-load="true" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 02. 商品数据区 -->
    <view class="goods-area bg-main padding">
      <view class="price-box dflex-b">
        <view>
          <text class="price fwb fs-big">{{ actualPrice || '' }}</text>
          <text class="m-price" v-if="originalPrice > 0">{{ originalPrice || '' }}</text>
        </view>
        <view class="dflex fs-sm ft-dark">
          <view class="margin-right-sm dflex">
            <view class="padding-right-xs padding-right-xs">已售</view>
            <text>{{ goodsSales || '0' }}</text>
          </view>
          <view class="dflex">
            <view class="padding-right-xs padding-right-xs">库存</view>
            <text>{{ goodsStock || '0' }}</text>
          </view>
        </view>
      </view>
      <text class="title fs">{{ goods.name || '' }} {{ sku.name || '' }}</text>
    </view>
    <view class="gap"></view>

    <!-- 03. 规格区 -->
    <view v-if="skuList.length > 0" class="sku-area bg-main padding-lr padding-top padding-bottom-xs pos-r">
      <view class="con dflex dflex-wrap-w">
        <view
          class="margin-right-sm margin-bottom-sm dflex bg-drak border-radius-lg padding-tb-16 padding-lr"
          :class="{ active: item.selected }"
          v-for="(item, index) in skuList"
          :key="index"
          @click="selectSku(item)"
        >
          <text class="fs-xs">{{ item.name }}</text>
        </view>
      </view>
    </view>
    <view v-if="skuList.length > 0" class="gap"></view>

    <!--阶梯价格-->
    <view>
      <view v-for="item in tieredPriceList" :key="item.id">
        <view class="dflex-b">
          <view class="dflex">
            <text class="margin-right">购买数量</text>
            <text>{{ item.minQuantity }}件以上</text>
          </view>
          <view class="price">{{ item.price }}</view>
        </view>
      </view>
    </view>



    <!-- 04.01 优惠券 -->
    <use-list-title title="优惠" tip="领取优惠券" color="#428675" iconfont="iconyouhui"
                    @goto="$refs.couponPopup.open()"></use-list-title>
    <!-- 04.01 优惠券弹出层 -->
    <uni-popup type="bottom" ref="couponPopup">
      <!-- 优惠券区 -->
      <scroll-view v-if="couponList && couponList.length">
        <view class="coupon-area padding bg-drak">
          <view class="coupon-item bg-main pos-r fs-xs" v-for="(item, index) in couponList" :key="index">
            <view class="content pos-r padding dflex-b">
              <view class="">
                <view class="margin-bottom-xs fs">{{ item.name }}</view>
                <view class="ft-dark">有效期至 {{ item.validEndTime.split(' ')[0] }}</view>
              </view>
              <view class="tar">
                <view class="margin-bottom-xs price">{{ item.discountAmount }}</view>
                <view v-if="item.minOrderAmount > 0" class="ft-dark">满{{ item.minOrderAmount }}可用</view>
                <view v-else class="ft-dark">不限</view>
              </view>

              <view class="circle l"></view>
              <view class="circle r"></view>
            </view>
            <view class="dflex-b">
              <text class="ft-dark padding-lr"></text>
              <text class="ft-base padding-tb-sm padding-lr" @click="pickCoupon(item.id)">立即领取</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <view v-else class="coupon-area dflex-c bg-white">暂无优惠券</view>
    </uni-popup>

    <!-- 04.02 服务标签 -->
    <view class="bg-main padding-lr padding-top padding-bottom-xs pos-r" @click="$refs.tagPopup.open()">
      <view class="dflex dflex-wrap-w">
        <view v-for="(item, index) in goodsTags" :key="index" class="margin-right-sm margin-bottom-sm dflex">
          <view class="iconfont iconyiwancheng- fwb fs-xs ft-base margin-right-xs"></view>
          <text class="fs-xs">{{ item.tag.name }}</text>
        </view>
      </view>
      <view class="icon-detail pos-a">
        <view class="iconfont iconxiangqing ft-dark fs-sm"></view>
      </view>
    </view>
    <view class="gap"></view>
    <!-- 04.02 服务标签弹出层 -->
    <uni-popup type="bottom" ref="tagPopup">
      <view class="bg-white">
        <view class="tac w-full padding-sm">服务说明</view>
        <view class="padding-lr padding-bottom-sm">
          <view v-for="(item, index) in goodsTags" :key="index" class="margin-right-sm margin-bottom-sm dflex dflex-s">
            <view class="iconfont iconyiwancheng- fwb fs ft-base margin-right-xs"></view>
            <view>
              <view class="fs-sm">{{ item.tag.name }}</view>
              <view class="fs-xs">{{ item.tag.description }}</view>
            </view>
          </view>
        </view>
      </view>

    </uni-popup>

    <!-- 05. 评价区 -->
    <view class="evaluate-area" v-if="commentList.length > 0">
      <use-list-title title="商品评价" tip="好评率 100%" color="#ff6a6c" iconfont=" "></use-list-title>
      <view class="padding-lr bg-main">
        <view class="eva-box dflex-s padding-bottom-lg" v-for="(item, index) in commentList" :key="index">
          <image class="portrait border-radius-c" :src="item.user.avatar || '/static/images/logo-mini.png'"></image>
          <view class="right-area flex1 padding-left-sm">
            <view class="dflex-b ft-dark">
              <view class="dflex">
                <text class="name margin-right">{{ item.user.nickname || item.user.username || '优选用户' }}</text>
                <use-rate :value="item.score" disabled></use-rate>
              </view>
              <text class="time fs-xs">{{ item.createTime }}</text>
            </view>
            <view class="fs-sm ft-main padding-top-xs padding-bottom-sm">{{ item.content }}</view>
            <view class="dflex dflex-wrap-w">
              <image
                class=""
                mode="widthFix"
                v-for="(img, i) in item.images"
                :lazy-load="true"
                :key="i"
                :src="img"
                @click="preview(item.images, img)"
              ></image>
            </view>
            <view class=" mt-2" v-if="item.replyContent ">
              <text class="text-mint">商家回复：{{ item.replyContent }}</text>
            </view>
          </view>


        </view>

      </view>

      <view class="gap"></view>
    </view>

    <!-- 06. 详情区 -->
    <view class="detail-area bg-main">
      <view class="d-header padding dflex-c">
        <text>图文详情</text>
      </view>
      <rich-text class="pro-detail" :nodes="goods.content"></rich-text>
    </view>

    <!-- 07. 操作区 -->
    <view class="oper-area pos-f dflex-b w-full padding-lr-sm">
      <view class="btn-area dflex dflex-flow-c" @click="toHome">
        <text class="iconfont iconshouye-1"></text>
        <text>首页</text>
      </view>

      <view class="btn-area dflex dflex-flow-c" @click="toggleFavorite">
        <text class="iconfont" :class="favorite ? 'iconshoucang- text-mint' : 'iconshoucang-01'"></text>
        <text :class="favorite ? 'text-mint' : ''">收藏</text>
      </view>
      <view class="flex1 btn-container dflex-b border-radius-big">
        <view class="tac padding-tb-sm flex1 bg-mint-light text-mint" v-if="goodsStock > 0" @click="addCart()">加入购物车
        </view>
        <view class="tac padding-tb-sm flex1 bg-mint text-white" v-if="goodsStock > 0" @click="buyNow()">立即购买</view>
        <view class="tac padding-tb-sm flex1 bg-disabled" v-else>已售磐</view>
      </view>
    </view>


    <use-login-form ref="loginPopup"></use-login-form>

    <!-- 置顶 -->
    <use-totop ref="usetop" bottom="120"></use-totop>
  </view>
</template>


<script setup>
import {ref} from 'vue'
import {onLoad, onShow} from '@dcloudio/uni-app'
import {getGoodsDetailData, getGoodsTagListData, getSkuListData, getGoodsCommentListData} from "/common/api/goods"
import {getCustomerPriceData, getTieredPricingListData} from "/common/api/goods";
import {getIsFavoriteData, postFavoriteToggleData} from "@/common/api/favorite"
import {putCartAddData} from "@/common/api/cart"
import {putFootprintData} from "@/common/api/footprint"
import {useWxJs} from "@/common/utils";
import {getGoodsCouponListData, putGetCouponData} from "@/common/api/coupon";

import {useUserStore} from "@/stores/user";

const {share} = useWxJs()

const userStore = useUserStore()

const loginPopup = ref(null)

const id = ref(0)
const goods = ref({})
const skuList = ref([])
const sku = ref({})
const goodsStock = ref(0)
const goodsSales = ref(0)
const actualPrice = ref(0)
const originalPrice = ref(0)
const goodsTags = ref([])
const favorite = ref(false)
const commentList = ref([])
const tieredPriceList = ref([])

const preview = () => {
}

const couponList = ref([])


// 选择sku
const selectSku = (obj) => {
  const list = [...skuList.value]
  list.forEach(item => {
    item.selected = item.id === obj.id;
  })
  skuList.value = list
  sku.value = obj
  actualPrice.value = obj.actualPrice
  originalPrice.value = obj.originalPrice
  goodsSales.value = obj.sales
  goodsStock.value = obj.stock

  getCustomerPrice()
  listTieredPricing()

}

// 获取商品基础信息
const getGoodsDetail = async () => {
  const res = await getGoodsDetailData(id.value)
  res.imageList = res.imageList.split(',')
  goods.value = res
  actualPrice.value = res.actualPrice
  originalPrice.value = res.originalPrice

  await share(goods.value.name, goods.value.image)
}

// 获取sku列表
const getSkuList = async () => {
  const list = await getSkuListData(id.value)
  if (list && list.length > 0) {
    let stock = 0
    let sales = 0
    skuList.value = list.map((item) => {
      stock += item.stock
      sales += item.sales
      item.selected = false
      return item
    })
    goodsStock.value = stock
    goodsSales.value = sales

    selectSku(skuList.value[0])
  }
}
// 获取tags
const getGoodsTags = async () => {
  goodsTags.value = await getGoodsTagListData(id.value)
}

// 检查收藏
const checkFavorite = async () => {
  favorite.value = await getIsFavoriteData(id.value)
}
// 切换收藏
const toggleFavorite = async () => {
  const title = await postFavoriteToggleData(id.value)
  uni.showToast({title})
  await checkFavorite()
}

const addCart = async () => {
  if (goodsStock <= 0) {
    uni.showToast({title: '已售罄', icon: 'none'})
    return
  }
  if (!sku.value.id) {
    uni.showToast({title: '请选择商品规格', icon: 'none'})
    return
  }
  const res = await putCartAddData({skuId: sku.value.id, quantity: 1})
  res && uni.showToast({title: '添加成功', icon: 'success'})

}

const listComment = async () => {
  const data = await getGoodsCommentListData(id.value)
  if (data && data.length > 0) {
    commentList.value = data.map(item => {
      return {
        ...item,
        images: item.images.split(','),
        createTime: item.createTime.substring(0, 10)
      }
    })
  }
}

const listTieredPricing = async () => {
  tieredPriceList.value = await getTieredPricingListData(sku.value.id)
}

const getCustomerPrice = async () => {
  if (!userStore.isLogin) {
    return
  }
  const res = await getCustomerPriceData(sku.value.id)
  if (res){
    actualPrice.value = res
  }
}

const buyNow = async () => {
  if (goodsStock <= 0) {
    uni.showToast({title: '已售罄', icon: 'none'})
    return
  }
  if (!sku.value.id) {
    uni.showToast({title: '请选择商品规格', icon: 'none'})
    return
  }
  if (!userStore.isLogin) {
    userStore.showLogin()
    return
  }
  uni.navigateTo(
    {
      url: '/pages/order/create?sku_id=' + sku.value.id
    }
  )
}
const addFootprint = () => {
  putFootprintData(id.value)
}

const toHome = () => {
  uni.switchTab({
    url: '/pages/tabbar/home'
  })
}

const listCoupon = async () => {
  couponList.value = await getGoodsCouponListData(id.value)
}

const pickCoupon = async (id) => {
  try {
    await putGetCouponData(id)
    uni.showToast({title: '领取成功'})
  } catch (e) {
    uni.showToast({title: e, icon: 'none'})
  }

}

const init = () => {
  getGoodsDetail()
  getSkuList()
  getGoodsTags()
  if (userStore.isLogin) {
    checkFavorite()
    addFootprint()
  }
  listCoupon()
  listComment()
}
onLoad((params) => {
  id.value = params.id
})

onShow(() => {
  init()
})

</script>

<style lang="scss" scoped>
page {
  background: $page-color-base;
  padding-bottom: 120rpx;
}

contact-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
}

.fixed-top {
  bottom: 230rpx;
}

/* 01. 轮播区 */
.swiper-area {
  height: 720rpx;
  top: 0;
  z-index: -1;
}


/* 02. 商品数据区 */
.goods-area {
  margin-top: 720rpx;

  .price-box {
    display: flex;
    align-items: baseline;
  }

  .title {
    color: $font-color-dark;
    height: 46rpx;
    line-height: 46rpx;
  }
}

.share-area {
  .vertical-line {
    right: 50%;
    height: 40%;
  }
}

/* 03. 规格区 */
.sku-area .active {
  background: $base-color;
  color: #fff !important;
}

/* 04. 服务区 */
.icon-detail {
  right: 30rpx;
  top: 24rpx;
}

/* 05. 评价 */
.evaluate-area {
  .portrait {
    flex-shrink: 0;
    width: 80rpx;
    height: 80rpx;
  }

  .right-area {
    image {
      margin-right: 10rpx;
      margin-bottom: 10rpx;
      height: 200rpx;
      width: 30%;
    }
  }
}

/* 06. 详情区 */
.detail-area {
  .d-header {
    font-size: $font-base + 2upx;
    color: $font-color-dark;
    position: relative;

    text {
      padding: 0 20rpx;
      background: #fff;
      position: relative;
      z-index: 1;
    }

    &:after {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translateX(-50%);
      width: 300rpx;
      height: 0;
      content: '';
      border-bottom: 1px solid #ccc;
    }
  }

  /* 产品详情 */
  .pro-detail {
    width: 100%;
    overflow: hidden;
    -webkit-touch-callout: none;

    img {
      width: 100%;
      max-width: 100%;
      overflow: hidden;
    }
  }
}

/* 07. 操作区 */
.oper-area {
  left: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 20rpx 0 #f0f0f0;
  height: 100rpx;
  z-index: 95;

  .btn-area {
    font-size: $font-sm;
    color: $font-color-base;
    width: 96rpx;

    .iconfont {
      font-size: 40rpx;
      line-height: 48rpx;
    }
  }
}

/* 优惠券区 */
.coupon-area {
  max-height: 60vh;
  min-height: 30vh;

  .coupon-item {
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .content {
      &:after {
        position: absolute;
        left: 0;
        bottom: 0;
        content: '';
        width: 100%;
        height: 0;
        border-bottom: 1px dashed #f3f3f3;
        transform: scaleY(50%);
      }
    }

    .circle {
      position: absolute;
      bottom: -10rpx;
      z-index: 10;
      width: 20rpx;
      height: 20rpx;
      background: #f5f5f5;
      border-radius: 50%;

      &.r {
        right: -6rpx;
      }

      &.l {
        left: -6rpx;
      }
    }
  }
}
</style>
