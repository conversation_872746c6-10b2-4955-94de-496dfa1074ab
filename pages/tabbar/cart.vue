<template>
  <view class="container bg-drak" :class="{ 'margin-bottom-big': !list.length === 0 }">
    <!-- 00. 未授权登录 -->
    <use-empty v-if="!userStore.isLogin" e-style="round" e-type="unauthorized" tip="当前未授权" btn-tip="去登录"
               height="70vh"
               :auto="false" @goto="$refs.loginPopup.open()"></use-empty>
    <!-- 00. 空白页 -->
    <use-empty v-else-if="list.length === 0" e-style="round" e-type="cart" tip="购物车数据为空"
               height="70vh"></use-empty>
    <!-- 00. 列表 -->
    <view v-else>
      <!-- 01. 购物车列表1 -->
      <view class="cart-list padding-sm">
        <template v-for="(item, index) in list" :key="index">
          <view class="cart-item bg-main margin-bottom-sm padding-lg pos-r dflex-s border-radius">
            <view class="image-wrapper pos-r" @click="toGoods(item.goodsId)">
              <!-- 商品图片 -->
              <image class="border-radius-xs wh-full" mode="aspectFill" :lazy-load="true"
                     :src="item.goodsImage">
              </image>
              <!-- 选中|未选中按钮 -->
              <view v-if="item.stock > 0 && item.stock >= item.quantity"
                    class="iconfont checkbox pos-a bg-main border-radius-big"
                    :class="{active: item.checked, iconxuanzhongzhuangtai: item.checked, iconweixuanzhongzhuangtai: !item.checked}"
                    @tap.stop="check(index)"></view>

              <view v-if="(item.stock < 10 || item.stock < item.quantity)"
                    class="disabled dflex-c dflex-flow-c pos-a pos-tl-c border-radius-c">
                <text>库存不足</text>
                <text class="margin-left-xs fs-xs" v-if="item.stock > 0">剩余
                  {{ item.stock }}
                </text>
              </view>
            </view>
            <view class="item-right padding-left pos-r">
              <!-- 商品名称 -->
              <view class="clamp-2 title" @click="toGoods(item.goodsId)">{{ item.goodsName }}
                {{ item.skuName }}
              </view>
              <view class="ft-dark fs-xs padding-top-xs">{{ item.skuName || '&nbsp;&nbsp;' }}</view>
              <view class="padding-tb-sm">
                <text class="price">{{ item.actualPrice }}</text>
                <text class="m-price"
                      v-if="item.originalPrice > 0">{{ item.originalPrice }}
                </text>
              </view>

              <!-- + - 购物车数量 -->
              <use-number-box :min="1" :max="item.stock || 1" :value="item.quantity"
                              :is-max="item.quantity >= item.stock" :is-min="item.quantity === 1"
                              :index="index" :disabled="item.quantity >= item.stock"
                              @eventChange="handleNumberChange">
              </use-number-box>
            </view>

            <!-- 删除 -->
            <view class="del-btn iconfont iconlajitong-01 pos-a border-radius-c dflex-c ft-dark fs-xl"
                  @tap.stop="deleteCart(item.skuId)"></view>
          </view>
        </template>
      </view>

      <!-- 02. 底部操作栏 -->
      <view class="action-section dflex w-full bg-main pos-f padding-right">
        <view class="checkbox pos-r h-full dflex-c">
          <view class="padding-lr iconfont"
                :class="{active:allChecked,iconxuanzhongzhuangtai: allChecked, iconweixuanzhongzhuangtai: !allChecked}"
                @click="checkAll()"></view>
          <view class="clear-btn pos-a tac ft-white" :class="{ show: allChecked }" @click="clearCart">清空
          </view>
        </view>
        <view class="total-box flex1 tar padding-right-lg">
          <text class="price">{{ total || 0 }}</text>
        </view>
        <button type="primary" class="payment no-border border-radius-lg fs" @click="createOrder">去结算</button>
      </view>
    </view>

    <!-- 03. 猜你喜欢 -->
    <use-hot-goods title-type="round" title="热门推荐" v-if="false"></use-hot-goods>

    <use-login-form ref="loginPopup"/>
  </view>
</template>

<script setup>
import {ref, computed} from 'vue'
import {onShow, onPullDownRefresh} from '@dcloudio/uni-app'
import {deleteAllCartData, getCartListData, postCartUpdateData} from "@/common/api/cart"

import {useUserStore} from "@/stores/user"

const userStore = useUserStore()

const allChecked = ref(false)
const total = computed(() => {
  return list.value.filter(item => item.checked).reduce((pre, cur) => {
    return pre + cur.actualPrice * cur.quantity
  }, 0)
})

const list = ref([])
const getList = async () => {
  const _list = await getCartListData()
  if (_list && Array.isArray(_list)) {
    list.value = _list.map(item => {
      item.checked = false
      return item
    })
  }

}

// 删除某一项
const deleteCart = async (skuId) => {
  await postCartUpdateData({skuId, quantity: 0})
  await getList()
}

// 调整购物车数量
const handleNumberChange = (data) => {
  const item = list.value[data.index]
  item.quantity = data.number
  postCartUpdateData({skuId: item.skuId, quantity: item.quantity})
}

// 清空购物车
const clearCart = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空购物车吗？',
    success: async (res) => {
      if (res.confirm) {
        await deleteAllCartData()
        await getList()
      }
    }
  })
}

const check = (index) => {
  list.value[index].checked = !list.value[index].checked
  allChecked.value = list.value.every(item => item.checked)
}

const checkAll = () => {
  allChecked.value = !allChecked.value
  const _list = [...list.value]
  list.value = _list.map(item => {
    item.checked = allChecked.value
    return item
  })
}

const createOrder = () => {
  const ids = list.value.filter(item => item.checked).map(item => item.skuId)
  if (ids.length > 0) {
    uni.navigateTo({
      url: `/pages/order/create?cart_ids=${ids}`
    })
  } else {
    uni.showToast({
      title: '请选择商品',
      icon: 'none'
    })
  }
}

const toGoods = (id) => {
  uni.navigateTo({
    url: `/pages/goods/goods?id=${id}`
  })
}
onPullDownRefresh(async () => {
  await getList()
  uni.stopPullDownRefresh()
})
onShow(() => {
  getList()
})
</script>

<style lang='scss'>
page {
  min-height: 100%;
}

/* 购物车列表项 */
.cart-item {

  &:last-child {
    margin-bottom: 0;
  }

  .image-wrapper {
    width: 230rpx;
    height: 230rpx;
    flex-shrink: 0;

    image {
      opacity: 1;
    }
  }

  .checkbox {
    top: -16rpx;
    left: -16rpx;
    color: $font-color-disabled;
    line-height: 1;
    font-size: 46rpx;
    padding: 5rpx;
    z-index: 8;
  }

  .disabled {
    color: #fff !important;
    width: 70%;
    height: 70%;
    background-color: rgba(51, 51, 51, 0.5);
  }

  .item-right {
    height: 260rpx;
    overflow: hidden;
  }

  .del-btn {
    bottom: 40rpx;
    right: 30rpx;
    width: 70rpx;
    height: 70rpx;
  }
}

/* 底部栏 */
.action-section {
  z-index: 999;
  bottom: 0;
  height: 100rpx;

  .checkbox {
    .iconfont {
      font-size: 46rpx;
      color: #2C405A;
    }
  }

  .clear-btn {
    left: 100rpx;
    background: #2C405A;
    border-radius: 0 50rpx 50rpx 0;
    padding: 12rpx 0;
    transition: all .2s;

    width: 0;
    opacity: 0;

    &.show {
      width: 120rpx;
      opacity: 1;
    }
  }

  .payment {
    padding: 0 40rpx;
    font-size: $font-base;
    background: $uni-color-primary;
    box-shadow: 1px 2px 5px rgba(66, 134, 117, 0.72)
  }
}

/* #ifdef H5 || MP-360 */
.action-section {
  margin-bottom: 50px;
}

/* #endif */
</style>
